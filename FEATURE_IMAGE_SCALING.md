# 书签图片缩放功能

## 功能概述

为书签编辑和新增功能添加了图片缩放功能，支持用户在使用自定义图片时（URL链接或本地上传）对图片进行缩放、位置调整和旋转。

## 新增功能

### 1. 图片缩放组件 (`ImageScaler`)

**位置**: `src/components/ui/ImageScaler.tsx`

**功能特性**:
- 实时图片预览
- 缩放比例调整 (10% - 300%)
- 水平/垂直位置调整 (-100% 到 100%)
- 旋转角度调整 (0° - 360°)
- 拖拽调整位置
- 重置功能
- 实时生成最终图片

**使用方法**:
```tsx
<ImageScaler
  imageUrl={originalImageData}
  config={scaleConfig}
  onConfigChange={handleScaleChange}
  onImageGenerated={handleImageGenerated}
  size={64}
/>
```

### 2. 图片处理工具函数

**位置**: `src/utils/icon-processing.utils.ts`

**新增函数**:
- `applyImageScale()`: 应用缩放配置生成最终图片
- `compressAndScaleImage()`: 压缩并应用缩放配置

### 3. 类型定义

**位置**: `src/types/bookmark-style.types.ts`

**新增类型**:
```typescript
interface ImageScaleConfig {
  scale: number;        // 缩放比例 (0.1-3.0)
  offsetX: number;      // X轴偏移 (-100 到 100)
  offsetY: number;      // Y轴偏移 (-100 到 100)
  rotation?: number;    // 旋转角度 (0-360)
}

interface BookmarkItem {
  // ... 其他属性
  imageScale?: ImageScaleConfig; // 图片缩放配置
}
```

## 更新的组件

### 1. IconSelector 组件

**位置**: `src/components/bookmarks/IconSelector.tsx`

**更新内容**:
- 添加图片缩放配置支持
- 集成 ImageScaler 组件
- 添加设置按钮用于打开缩放器
- 自动应用缩放配置生成最终图片

### 2. BookmarkModal 组件

**位置**: `src/components/bookmarks/BookmarkModal.tsx`

**更新内容**:
- 添加图片上传和缩放功能
- 支持本地图片文件上传
- 集成图片缩放器
- 保存缩放配置到书签数据

### 3. BookmarkModalNew 组件

**位置**: `src/components/bookmarks/BookmarkModalNew.tsx`

**更新内容**:
- 在图片图标选项卡中添加本地上传功能
- 集成图片缩放器
- 支持URL和本地上传两种方式

## 使用流程

### 1. 添加/编辑书签时使用图片缩放

1. 打开书签编辑弹窗
2. 选择"图片"图标类型
3. 可以选择：
   - 输入图片URL
   - 或上传本地图片文件
4. 上传图片后，点击设置按钮打开缩放器
5. 调整缩放、位置和旋转参数
6. 点击"完成编辑"应用设置
7. 保存书签

### 2. 缩放控制

- **缩放**: 使用滑块或+/-按钮调整 (10%-300%)
- **位置**: 拖拽预览区域或使用滑块精确调整
- **旋转**: 使用旋转按钮(±90°)或滑块精确调整
- **重置**: 一键恢复默认设置

## 技术实现

### 1. Canvas 图片处理

使用 HTML5 Canvas API 进行图片处理：
- 支持缩放、平移、旋转变换
- 实时预览效果
- 生成最终的 base64 图片数据

### 2. 响应式设计

- 支持不同尺寸的输出图片
- 自适应预览区域大小
- 移动端友好的触控操作

### 3. 性能优化

- 图片压缩减少存储空间
- 实时预览避免重复计算
- 内存管理防止泄漏

## 测试功能

**位置**: `src/components/test/ImageScalerTest.tsx`

添加了专门的测试页面，可以通过主页右上角的测试按钮访问，用于：
- 测试图片缩放功能
- 验证不同图片格式的兼容性
- 测试书签弹窗中的集成效果

## 文件清单

### 新增文件
- `src/components/ui/ImageScaler.tsx` - 图片缩放组件
- `src/components/test/ImageScalerTest.tsx` - 测试页面

### 修改文件
- `src/types/bookmark-style.types.ts` - 添加类型定义
- `src/utils/icon-processing.utils.ts` - 添加图片处理函数
- `src/components/bookmarks/IconSelector.tsx` - 集成缩放功能
- `src/components/bookmarks/BookmarkModal.tsx` - 添加上传和缩放
- `src/components/bookmarks/BookmarkModalNew.tsx` - 添加上传和缩放
- `src/pages/newtab/NewTabApp.tsx` - 添加测试模式

## 兼容性

- 支持所有现代浏览器
- 兼容 Chrome 扩展环境
- 支持常见图片格式 (JPG, PNG, GIF, WebP)
- 文件大小限制: 2MB

## 后续优化建议

1. 添加更多预设缩放比例
2. 支持批量图片处理
3. 添加图片滤镜效果
4. 支持图片裁剪功能
5. 添加撤销/重做功能
