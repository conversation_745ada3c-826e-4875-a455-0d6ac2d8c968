/* 导入共享样式 */
@import '../../styles/shared.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Popup页面专用样式 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden; /* 防止popup出现滚动条 */
}

* {
  box-sizing: border-box;
}

/* 针对popup的小尺寸优化 */
.popup-container {
  width: 400px;
  height: 600px;
  overflow-y: auto;
}

.popup-container::-webkit-scrollbar {
  width: 6px;
}

.popup-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.popup-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.popup-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
