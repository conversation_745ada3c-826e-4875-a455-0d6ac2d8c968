/**
 * 共享样式文件 - 避免重复定义
 */

@layer utilities {
  /* 统一的字体定义 */
  .font-system {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 统一的滚动条样式 */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 小尺寸滚动条(用于popup) */
  .custom-scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .custom-scrollbar-thin::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .custom-scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 隐藏滚动条但保持功能 */
  .hide-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  /* 通用过渡动画 */
  .smooth-transition {
    transition: all 0.3s ease;
  }

  .smooth-transition-fast {
    transition: all 0.2s ease;
  }

  /* 通用悬停效果 */
  .hover-lift:hover {
    transform: translateY(-2px);
  }

  .hover-scale:hover {
    transform: scale(1.05);
  }

  /* 通用阴影效果 */
  .shadow-soft {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .shadow-medium {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  .shadow-strong {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  }
}
