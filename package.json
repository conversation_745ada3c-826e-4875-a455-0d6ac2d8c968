{"name": "dualtab", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && node scripts/post-build.js", "build:check": "tsc -b && vite build && node scripts/post-build.js", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "postcss": "^8.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.16"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/chrome": "^0.0.323", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}